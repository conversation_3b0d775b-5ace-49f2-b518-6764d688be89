const express = require('express');
const mongoose = require('mongoose');
const dotenv = require('dotenv');

// Load environment variables
dotenv.config();

// Connect to MongoDB
const connectDB = async () => {
  try {
    await mongoose.connect(process.env.MONGODB_URI || 'mongodb://localhost:27017/wasel');
    console.log('✅ MongoDB connected successfully');
  } catch (error) {
    console.error('❌ MongoDB connection error:', error);
    process.exit(1);
  }
};

// Supplier Schema (simplified for this script)
const SupplierSchema = new mongoose.Schema({
  id: String,
  name: String,
  rating: Number,
  ratings: Number,
  // ... other fields
}, { strict: false }); // Allow additional fields

const Supplier = mongoose.model('Supplier', SupplierSchema);

async function testApiEndpoint() {
  try {
    console.log('🧪 Testing API endpoint simulation...');
    
    // Connect to database
    await connectDB();

    // Simulate the exact same query as the controller
    const supplier = await Supplier.findOne({ id: '3a-kefak', isActive: true })
      .select('-__v');

    if (!supplier) {
      console.log('❌ Supplier not found');
      return;
    }

    // Simulate the API response
    const apiResponse = {
      success: true,
      data: supplier
    };

    console.log('📡 Simulated API Response:');
    console.log(JSON.stringify(apiResponse, null, 2));
    
    // Check specifically for ratings field
    console.log('\n🔍 Ratings field check:');
    console.log(`Has ratings field: ${supplier.ratings !== undefined}`);
    console.log(`Ratings value: ${supplier.ratings}`);

    process.exit(0);
  } catch (error) {
    console.error('❌ Error during test:', error);
    process.exit(1);
  }
}

// Run the test
testApiEndpoint();
