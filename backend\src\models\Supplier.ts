import mongoose, { Document, Schema } from 'mongoose';

export interface IProduct {
  id: string;
  name: string;
  image: string;
  price: number;
  discountPrice?: number;
  category: string;
  description?: string;
  isAvailable: boolean;
  restaurantOptions?: {
    additions?: Array<{ id: string; name: string; price: number }>;
    without?: string[];
    sides?: Array<{ id: string; name: string; price: number }>;
  };
  clothingOptions?: {
    sizes: string[];
    colors: string[];
    gallery: string[];
  };
}

export interface ISupplier extends Document {
  id: string;
  name: string;
  lat: number;
  lng: number;
  category: string;
  rating: number;
  tags: string[];
  logoUrl: string;
  banner: string;
  openHours: string;
  deliveryTime: string;
  phone: string;
  address?: string;
  description?: string;
  isActive: boolean;
  products: IProduct[];
  createdAt: Date;
  updatedAt: Date;
  ratings: number;
}

const ProductSchema: Schema = new Schema({
  id: { type: String, required: true },
  name: { type: String, required: true, trim: true },
  image: { type: String, required: true },
  price: { type: Number, required: true, min: 0 },
  discountPrice: { type: Number, min: 0 },
  category: { type: String, required: true, trim: true },
  description: { type: String, trim: true },
  isAvailable: { type: Boolean, default: true },
  restaurantOptions: {
    additions: [{
      id: { type: String, required: true },
      name: { type: String, required: true },
      price: { type: Number, required: true, min: 0 }
    }],
    without: [{ type: String }],
    sides: [{
      id: { type: String, required: true },
      name: { type: String, required: true },
      price: { type: Number, required: true, min: 0 }
    }]
  },
  clothingOptions: {
    sizes: [{ type: String }],
    colors: [{ type: String }],
    gallery: [{ type: String }]
  }
});

const SupplierSchema: Schema = new Schema({
  id: {
    type: String,
    required: true,
    unique: true,
    trim: true
  },
  name: {
    type: String,
    required: true,
    trim: true
  },
  lat: {
    type: Number,
    required: true
  },
  lng: {
    type: Number,
    required: true
  },
  category: {
    type: String,
    required: true,
    trim: true
  },
  rating: {
    type: Number,
    required: true,
    min: 0,
    max: 5
  },
  tags: [{
    type: String,
    trim: true
  }],
  logoUrl: {
    type: String,
    required: true
  },
  banner: {
    type: String,
    required: true
  },
  openHours: {
    type: String,
    required: true,
    trim: true
  },
  deliveryTime: {
    type: String,
    required: true,
    trim: true
  },
  phone: {
    type: String,
    required: true,
    trim: true
  },
  address: {
    type: String,
    trim: true
  },
  description: {
    type: String,
    trim: true
  },
  isActive: {
    type: Boolean,
    default: true
  },
  ratings: {
    type: Number,
    required: true,
    min: 0
  },
  products: [ProductSchema]
}, {
  timestamps: true
});

// Indexes for efficient queries
SupplierSchema.index({ category: 1, isActive: 1 });
SupplierSchema.index({ lat: 1, lng: 1 });
SupplierSchema.index({ name: 'text', 'products.name': 'text' });
SupplierSchema.index({ rating: -1 });

export const Supplier = mongoose.model<ISupplier>('Supplier', SupplierSchema);
