const mongoose = require('mongoose');
const dotenv = require('dotenv');

// Load environment variables
dotenv.config();

// Connect to MongoDB
const connectDB = async () => {
  try {
    await mongoose.connect(process.env.MONGODB_URI || 'mongodb://localhost:27017/wasel');
    console.log('✅ MongoDB connected successfully');
  } catch (error) {
    console.error('❌ MongoDB connection error:', error);
    process.exit(1);
  }
};

// Supplier Schema (simplified for this script)
const SupplierSchema = new mongoose.Schema({
  id: String,
  name: String,
  rating: Number,
  ratings: Number,
  // ... other fields
}, { strict: false }); // Allow additional fields

const Supplier = mongoose.model('Supplier', SupplierSchema);

async function checkRatingsField() {
  try {
    console.log('🔍 Checking ratings field in database...');
    
    // Connect to database
    await connectDB();

    // Find all suppliers
    const suppliers = await Supplier.find({}).select('id name rating ratings');

    console.log(`📊 Found ${suppliers.length} suppliers in database:`);
    
    suppliers.forEach(supplier => {
      console.log(`\n📋 Supplier: ${supplier.name} (${supplier.id})`);
      console.log(`   Rating: ${supplier.rating}`);
      console.log(`   Ratings: ${supplier.ratings}`);
      console.log(`   Has ratings field: ${supplier.ratings !== undefined}`);
    });

    // Also check the specific supplier from your error
    console.log('\n🔍 Checking specific supplier "3a-kefak":');
    const specificSupplier = await Supplier.findOne({ id: '3a-kefak' });
    if (specificSupplier) {
      console.log('Full supplier object:');
      console.log(JSON.stringify(specificSupplier, null, 2));
    } else {
      console.log('❌ Supplier "3a-kefak" not found');
    }

    process.exit(0);
  } catch (error) {
    console.error('❌ Error during check:', error);
    process.exit(1);
  }
}

// Run the check
checkRatingsField();
