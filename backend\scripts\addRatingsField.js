const mongoose = require('mongoose');
const dotenv = require('dotenv');

// Load environment variables
dotenv.config();

// Connect to MongoDB
const connectDB = async () => {
  try {
    await mongoose.connect(process.env.MONGODB_URI || 'mongodb://localhost:27017/wasel');
    console.log('✅ MongoDB connected successfully');
  } catch (error) {
    console.error('❌ MongoDB connection error:', error);
    process.exit(1);
  }
};

// Supplier Schema (simplified for this script)
const SupplierSchema = new mongoose.Schema({
  id: String,
  name: String,
  rating: Number,
  ratings: Number,
  // ... other fields
}, { strict: false }); // Allow additional fields

const Supplier = mongoose.model('Supplier', SupplierSchema);

async function addRatingsField() {
  try {
    console.log('🔄 Starting migration to add ratings field...');
    
    // Connect to database
    await connectDB();

    // Find all suppliers that don't have the ratings field
    const suppliersWithoutRatings = await Supplier.find({
      $or: [
        { ratings: { $exists: false } },
        { ratings: null }
      ]
    });

    console.log(`📊 Found ${suppliersWithoutRatings.length} suppliers without ratings field`);

    if (suppliersWithoutRatings.length === 0) {
      console.log('✅ All suppliers already have the ratings field');
      process.exit(0);
    }

    // Update each supplier with a default ratings value
    for (const supplier of suppliersWithoutRatings) {
      // Generate a realistic number of ratings based on the rating score
      // Higher rated suppliers tend to have more reviews
      let ratingsCount;
      if (supplier.rating >= 4.5) {
        ratingsCount = Math.floor(Math.random() * 200) + 100; // 100-300 ratings
      } else if (supplier.rating >= 4.0) {
        ratingsCount = Math.floor(Math.random() * 150) + 50; // 50-200 ratings
      } else if (supplier.rating >= 3.5) {
        ratingsCount = Math.floor(Math.random() * 100) + 30; // 30-130 ratings
      } else {
        ratingsCount = Math.floor(Math.random() * 50) + 10; // 10-60 ratings
      }

      await Supplier.updateOne(
        { _id: supplier._id },
        { $set: { ratings: ratingsCount } }
      );

      console.log(`✅ Updated ${supplier.name} (${supplier.id}) with ${ratingsCount} ratings`);
    }

    console.log('🎉 Migration completed successfully!');
    console.log(`📈 Updated ${suppliersWithoutRatings.length} suppliers with ratings field`);

    process.exit(0);
  } catch (error) {
    console.error('❌ Error during migration:', error);
    process.exit(1);
  }
}

// Run the migration
addRatingsField();
